<div class="modal " >
    <div class="modal-content">
        <h2><?php echo e($formMode === 'edit' ? 'Edit User' : 'Create User'); ?></h2>
        <span class="close-modal">×</span>
        <hr>
        <div>
            <label>Name</label>
            <input type="text" name="name" value="<?php echo e(isset($user->name) ? $user->name : ''); ?>"/>
            <?php echo $errors->first('name', '<p class="error">:message</p>'); ?>


            <label>Email</label>
            <input type="text" name="email" value="<?php echo e(isset($user->email) ? $user->email : ''); ?>"/>
            <?php echo $errors->first('email', '<p class="error">:message</p>'); ?>

            
            <label>Bio</label>
            <textarea cols="20" rows="3" name="bio"><?php echo e(isset($user->bio) ? $user->bio : ''); ?></textarea>
            
            <p>Type</p>
            <select name="role" id="">
                <option disabled selected>Select Role</option>
                <option value="admin" <?php echo e(isset($user->role) && $user->role == 'admin' ? 'selected' : ''); ?>>Admin</option>
                <option value="user" <?php echo e(isset($user->role) && $user->role == 'user' ? 'selected' : ''); ?>>Standard User</option>
            </select>

            <label>Password</label>
            <input  type="password" id="password" name="password" >
            <?php echo $errors->first('password', '<p class="error">:message</p>'); ?>

        </div>
        <hr>
        <div class="modal-footer">
            <button class="close-modal">
                Cancel
            </button>
            <button class="secondary close-modal">
                <span><i class="fa fa-spinner fa-spin"></i></span>
                <?php echo e($formMode === 'edit' ? 'Update User' : 'Save User'); ?>

            </button>
        </div>
    </div>
</div><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\resources\views/admin/users/form.blade.php ENDPATH**/ ?>