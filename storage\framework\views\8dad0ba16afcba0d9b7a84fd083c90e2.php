
<?php $__env->startSection('content'); ?>
<section class="users" id="users">
    <div class="titlebar">
        <h1>Users </h1>
        <button class="btn-icon success open-modal">New User</button>
    </div>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('admin.users.create', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="table">
        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="table-search">
            <div>
                <select class="search-select" name="" id="">
                    <option value="">Filter User</option>
                </select>
            </div>
            <div class="relative">
                <input class="search-input" type="text" name="search" placeholder="Search User...">
            </div>
        </div>
        <div class="user_table-heading">
            <p>Photo</p>
            <p>Name</p>
            <p>Email</p>
            <p>Role</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="user_table-items">
                <p>
                    <?php if($user->image): ?>
                        <img src="<?php echo e(asset('assets/img/' . $user->image)); ?>" alt="" class="user_img-list">
                    <?php else: ?>
                        <img src="<?php echo e(asset('assets/img/no-image.jpg')); ?>" alt="" class="user_img-list">
                    <?php endif; ?>
                </p>
                <p><?php echo e($user->name); ?></p>
                <p><?php echo e($user->email); ?></p>
                <p><?php echo e($user->role); ?></p>
                <div>
                    <button class="btn-icon success open-modal edit-user-btn" data-id="<?php echo e($user->id); ?>" data-name="<?php echo e($user->name); ?>" data-email="<?php echo e($user->email); ?>" data-bio="<?php echo e($user->bio); ?>" data-role="<?php echo e($user->role); ?>">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    <form method="POST" action="<?php echo e(route('admin.users.destroy', $user->id)); ?>" style="display: inline;" class="delete-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn-icon danger delete-btn" data-user-name="<?php echo e($user->name); ?>">
                            <i class="far fa-trash-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
            <?php echo $__env->make('admin.users.edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\resources\views/admin/users/index.blade.php ENDPATH**/ ?>